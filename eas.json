{"cli": {"version": ">= 12.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "android": {"buildType": "apk"}, "ios": {"buildConfiguration": "Debug"}}, "preview": {"distribution": "internal", "channel": "preview", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "ios": {"buildConfiguration": "Release", "simulator": false}, "env": {"NODE_ENV": "production"}}, "production": {"channel": "production", "android": {"buildType": "app-bundle"}, "ios": {"buildConfiguration": "Release"}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {}}}